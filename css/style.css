/* 样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 现代化设计变量 */
:root {
    /* 主色调 - 现代蓝色系 */
    --primary-color: #2563eb; /* 现代蓝色 */
    --primary-dark: #1d4ed8; /* 深蓝色 */
    --primary-light: #3b82f6; /* 浅蓝色 */
    --secondary-color: #1e40af; /* 辅助蓝色 */
    --accent-color: #06b6d4; /* 青色强调 */

    /* 中性色 */
    --text-primary: #1f2937; /* 主文本 */
    --text-secondary: #6b7280; /* 次要文本 */
    --text-muted: #9ca3af; /* 弱化文本 */

    /* 背景色 */
    --bg-primary: #ffffff; /* 主背景 */
    --bg-secondary: #f9fafb; /* 次要背景 */
    --bg-tertiary: #f3f4f6; /* 第三背景 */
    --bg-dark: #111827; /* 深色背景 */

    /* 边框和分割线 */
    --border-light: #e5e7eb;
    --border-medium: #d1d5db;
    --border-dark: #9ca3af;

    /* 状态色 */
    --success-color: #10b981; /* 成功绿 */
    --warning-color: #f59e0b; /* 警告橙 */
    --danger-color: #ef4444; /* 错误红 */
    --info-color: #3b82f6; /* 信息蓝 */

    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* 圆角 */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* 间距 */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-2xl: 4rem;

    /* 字体 */
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

body {
    font-family: var(--font-sans);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px; /* 调整内边距 */
}

/* 通用章节样式 */
.section {
    padding: 80px 0; /* 统一章节上下边距 */
    background-color: var(--white); /* 章节背景色 */
    margin-bottom: 30px; /* 章节间距 */
    box-shadow: 0 2px 10px rgba(0,0,0,0.05); /* 添加轻微阴影 */
}

.section:nth-child(odd) {
    background-color: var(--light-gray); /* 奇数章节使用浅灰色背景 */
}

.section-header {
    text-align: center;
    margin-bottom: 60px; /* 增加头部间距 */
}

.section-header h2 {
    font-size: 2.8em; /* 调整标题大小 */
    color: var(--dark-gray); /* 标题颜色 */
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.section-header h2::after {
    content: '';
    display: block;
    width: 60px;
    height: 4px;
    background: var(--primary-color);
    margin: 10px auto 0;
    border-radius: 2px;
}

.section-desc {
    font-size: 1.3em; /* 调整描述大小 */
    color: var(--dark-gray); /* 描述颜色 */
    max-width: 800px;
    margin: 0 auto;
}

/* 现代化导航栏 */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-bottom: 1px solid var(--border-light);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-lg);
    border-bottom-color: var(--border-medium);
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    height: 70px;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
    text-decoration: none;
    transition: transform 0.2s ease;
}

.logo:hover {
    transform: scale(1.05);
}

.logo-img {
    height: 36px;
    margin-right: var(--spacing-xs);
    border-radius: var(--radius-sm);
}

.logo-text {
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
    align-items: center;
}

.nav-link {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.95rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.nav-link:hover {
    color: var(--primary-color);
    background-color: rgba(37, 99, 235, 0.08);
    transform: translateY(-1px);
}

.nav-link.active {
    color: var(--primary-color);
    background-color: rgba(37, 99, 235, 0.12);
    font-weight: 600;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(-50%);
}

.nav-link:hover::before,
.nav-link.active::before {
    width: 80%;
}

.menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.menu-toggle span {
    display: block;
    width: 25px;
    height: 3px;
    background: var(--text-color);
    margin: 5px 0;
    transition: all 0.3s;
}

.menu-toggle.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.menu-toggle.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* 现代化Hero部分 */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    overflow: hidden;
    padding: 120px 0 80px;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
    color: white;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: var(--spacing-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.main-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: var(--spacing-md);
}

.title-highlight {
    display: block;
    color: var(--accent-color);
    font-size: 0.8em;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.title-main {
    display: block;
    background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.sub-title {
    font-size: 1.25rem;
    font-weight: 500;
    margin-bottom: var(--spacing-md);
    opacity: 0.9;
    color: rgba(255, 255, 255, 0.9);
}

.hero-text {
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: var(--spacing-xl);
    opacity: 0.85;
    max-width: 500px;
}

.hero-stats {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--accent-color);
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-top: var(--spacing-xs);
}

.hero-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.floating-cards {
    position: relative;
    width: 400px;
    height: 400px;
}

.floating-cards .card {
    position: absolute;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    animation: float 6s ease-in-out infinite;
}

.floating-cards .card i {
    font-size: 1.5rem;
    color: var(--accent-color);
}

.floating-cards .card-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-cards .card-2 {
    top: 50%;
    right: 10%;
    animation-delay: 2s;
}

.floating-cards .card-3 {
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../images/hero-bg.jpg') no-repeat center center/cover;
    z-index: 0;
    opacity: 0.1;
}

/* 现代化按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #f97316);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #f97316, #ea580c);
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.btn-large {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1.1rem;
    border-radius: var(--radius-lg);
}

/* 医保新规样式 */
.new-policy .policy-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); /* 调整最小宽度 */
    gap: 30px;
    margin-bottom: 60px;
}

.new-policy .policy-feature-card {
    background: var(--white);
    padding: 30px; /* 调整内边距 */
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08); /* 调整阴影 */
    transition: transform 0.3s ease-in-out;
}

.new-policy .policy-feature-card:hover {
    transform: translateY(-5px);
}

.new-policy .feature-icon {
    font-size: 2.2em; /* 调整图标大小 */
    color: var(--primary-color);
    margin-bottom: 20px;
}

.new-policy .policy-feature-card h4 {
    font-size: 1.6em; /* 调整标题大小 */
    color: var(--dark-gray);
    margin-bottom: 20px;
    border-bottom: 1px solid var(--medium-gray); /* 添加底部分隔线 */
    padding-bottom: 15px;
}

.new-policy .policy-feature-card ul {
    list-style: none;
    padding: 0;
}

.new-policy .policy-feature-card li {
    margin-bottom: 15px; /* 调整列表项间距 */
    display: flex;
    align-items: flex-start; /* 将这里从 center 改为 flex-start */
    gap: 12px; /* 调整图标与文本间距 */
    font-size: 1.1em;
    color: var(--text-color);
}

.new-policy .policy-feature-card li i {
    color: var(--success-color); /* 勾选图标颜色 */
}

.new-policy .policy-comparison-table {
    width: 100%;
    border-collapse: collapse;
    margin: 30px 0;
    box-shadow: 0 3px 15px rgba(0,0,0,0.05);
}

.new-policy .policy-comparison-table th:first-child,
.new-policy .policy-comparison-table td:first-child {
    width: 20%; /* 分配更多宽度给第一列 */
    white-space: nowrap; /* 防止文字换行 */
}

.new-policy .policy-comparison-table th,
.new-policy .policy-comparison-table td {
    padding: 15px; /* 调整表格内边距 */
    border: 1px solid var(--border-color);
    text-align: left;
    /* 其他列可以根据剩余空间自动分配 */
}

.new-policy .policy-comparison-table th {
    background: var(--medium-gray); /* 表头背景 */
    font-weight: 600;
    color: var(--dark-gray);
}

.new-policy .policy-comparison-table tr:nth-child(even) {
    background-color: var(--light-gray); /* 交替行背景色 */
}

/* 影响分析卡片 */
.new-policy .policy-impact-section h3 {
    font-size: 2em;
    color: var(--dark-gray);
    text-align: center;
    margin-bottom: 40px;
}

.new-policy .impact-cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); /* 调整最小宽度 */
    gap: 30px;
    margin: 0 0 60px 0; /* 调整外边距 */
}

.new-policy .impact-card {
    background: var(--white);
    padding: 30px; /* 调整内边距 */
    border-radius: 8px;
    box-shadow: 0 3px 15px rgba(0,0,0,0.08); /* 调整阴影 */
    text-align: center;
    transition: transform 0.3s ease-in-out;
}

.new-policy .impact-card:hover {
    transform: translateY(-5px);
}

.new-policy .impact-icon {
    font-size: 3em; /* 调整图标大小 */
    margin-bottom: 20px;
}

.new-policy .impact-card h5 {
    font-size: 1.4em; /* 调整标题大小 */
    color: var(--dark-gray);
    margin-bottom: 15px;
}

.new-policy .impact-card p {
    font-size: 1.1em;
    color: var(--text-color);
}

.hospital-impact { color: var(--primary-color); }
.patient-impact { color: var(--success-color); }
.industry-impact { color: var(--warning-color); }

/* 价格影响分析 */
.new-policy .policy-price-impact {
    background: var(--medium-gray); /* 调整背景色 */
    padding: 40px; /* 调整内边距 */
    border-radius: 10px;
    margin-bottom: 60px;
}

.new-policy .price-impact-title {
    font-size: 2em; /* 调整标题大小 */
    color: var(--dark-gray);
    margin-bottom: 30px;
    text-align: center;
}

.new-policy .price-impact-list {
    list-style: none; /* 移除默认列表样式 */
    padding: 0;
}

.new-policy .price-impact-list li {
    margin-bottom: 20px;
    padding-left: 25px; /* 调整内边距 */
    position: relative;
    font-size: 1.1em;
    color: var(--text-color);
}

.new-policy .price-impact-list li strong {
    color: var(--dark-gray);
}

.new-policy .price-impact-list li::before {
    content: "\2022"; /* 使用圆点作为列表标记 */
    position: absolute;
    left: 0;
    color: var(--primary-color); /* 标记颜色 */
    font-weight: bold;
}

/* 挑战与解决方案 */
.new-policy .our-value-section h3 {
    font-size: 2em;
    color: var(--dark-gray);
    text-align: center;
    margin-bottom: 40px;
}

.new-policy .challenge-solution-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); /* 调整最小宽度 */
    gap: 30px;
    margin-top: 0;
}

.new-policy .challenge-card,
.new-policy .solution-card {
    background: var(--white);
    padding: 30px; /* 调整内边距 */
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08); /* 调整阴影 */
}

.new-policy .challenge-icon,
.new-policy .solution-icon {
    font-size: 2.2em; /* 调整图标大小 */
    margin-bottom: 20px;
}

.new-policy .challenge-card h4,
.new-policy .solution-card h4 {
    font-size: 1.6em; /* 调整标题大小 */
    color: var(--dark-gray);
    margin-bottom: 20px;
    border-bottom: 1px solid var(--medium-gray); /* 添加底部分隔线 */
    padding-bottom: 15px;
}

.new-policy .challenge-card ul,
.new-policy .solution-card ul {
    list-style: none;
    padding: 0;
}

.new-policy .challenge-card li,
.new-policy .solution-card li {
    margin-bottom: 15px; /* 调整列表项间距 */
    display: flex;
    align-items: flex-start; /* 将这里从 center 改为 flex-start */
    gap: 12px; /* 调整图标与文本间距 */
    font-size: 1.1em;
    color: var(--text-color);
}

.new-policy .challenge-card li i {
    color: var(--danger-color); /* 图标颜色 */
}

.new-policy .solution-card li i {
    color: var(--success-color); /* 图标颜色 */
}
.new-policy .challenge-card p,
.new-policy .solution-card p {
    font-size: 1.1em;
    color: var(--text-color);
    margin-bottom: 20px;
}

.new-policy .solution-card strong {
    color: var(--dark-gray);
}

/* 核心优势样式 */
.services .service-highlights {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.services .highlight-card {
    background: var(--white);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    transition: transform 0.3s ease-in-out;
}

.services .highlight-card:hover {
    transform: translateY(-5px);
}

.services .card-img {
    width: 100%;
    height: 220px; /* 调整图片高度 */
    object-fit: cover;
}

.services .card-content {
    padding: 25px; /* 调整内边距 */
}

.services .card-content h3 {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
    font-size: 1.5em;
    color: var(--dark-gray);
}

.services .card-content h3 i {
    color: var(--primary-color);
}

.services .card-content p {
    font-size: 1.1em;
    color: var(--text-color);
}

.services .process-title {
    font-size: 2em;
    color: var(--dark-gray);
    text-align: center;
    margin-bottom: 40px;
}

.services .process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin: 40px 0 60px 0;
    position: relative;
}

.services .process-steps::before {
    content: '';
    position: absolute;
    top: 30px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--medium-gray);
    z-index: 0;
}

.services .process-step {
    text-align: center;
    position: relative;
    z-index: 1;
    background: var(--white); /* 步骤背景色 */
    padding-top: 20px; /* 调整内边距 */
}

.services .step-number {
    width: 50px; /* 调整数字圈大小 */
    height: 50px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-weight: bold;
    font-size: 1.3em;
    border: 3px solid var(--white); /* 数字圈边框 */
}

.services .process-step h4 {
    font-size: 1.3em;
    color: var(--dark-gray);
    margin-bottom: 10px;
}

.services .process-step p {
    font-size: 1em;
    color: var(--text-color);
}

.services .process-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    background: var(--medium-gray); /* 信息背景色 */
    padding: 30px; /* 调整内边距 */
    border-radius: 10px;
}

.services .info-item {
    text-align: center;
}

.services .info-item i {
    font-size: 2.5em;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.services .info-item h4 {
    font-size: 1.4em;
    color: var(--dark-gray);
    margin-bottom: 10px;
}

.services .info-item p {
    font-size: 1.1em;
    color: var(--text-color);
}

/* 为什么选择我们样式 */
.why-choose-us-main .why-choose-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); /* 调整最小宽度 */
    gap: 20px;
}

.why-choose-us-main .why-choose-item {
    background: var(--white);
    padding: 20px; /* 调整内边距 */
    border-left: 4px solid var(--primary-color); /* 左侧强调边框 */
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    display: flex;
    align-items: center;
    gap: 15px; /* 调整图标与文本间距 */
    font-size: 1.1em;
    color: var(--text-color);
}

.why-choose-us-main .why-choose-item i {
    color: var(--success-color); /* 图标颜色 */
    font-size: 1.3em;
}

/* 成功案例样式 */
.cases .cases-wrapper {
    display: flex; /* 使用flex布局 */
    gap: 40px;
    flex-wrap: wrap; /* 允许换行 */
}

.cases .case-images {
    flex: 1; /* 占据可用空间 */
    min-width: 300px; /* 最小宽度 */
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.cases .case-images img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.cases .case-images img.active {
    opacity: 1;
    position: static; /* 活动图片使用静态定位 */
}

.cases .case-content {
    flex: 2; /* 占据更多空间 */
    min-width: 350px; /* 最小宽度 */
    background: var(--white);
    padding: 30px; /* 调整内边距 */
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
}

.cases .case-tabs {
    display: flex;
    margin-bottom: 30px;
    border-bottom: 1px solid var(--border-color);
}

.cases .tab-btn {
    padding: 15px 20px; /* 调整内边距 */
    border: none;
    background: none;
    cursor: pointer;
    font-size: 1.1em;
    color: var(--text-color);
    transition: color 0.3s ease-in-out, border-bottom-color 0.3s ease-in-out;
    border-bottom: 3px solid transparent;
    margin-right: 20px;
}

.cases .tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    font-weight: 600;
}

.cases .tab-pane {
    display: none;
}

.cases .tab-pane.active {
    display: block;
}

.cases .tab-pane h3 {
    font-size: 1.6em;
    color: var(--dark-gray);
    margin-bottom: 20px;
}

.cases .tab-pane ul {
    list-style: none;
    padding: 0;
}

.cases .tab-pane li {
    margin-bottom: 15px;
    font-size: 1.1em;
    color: var(--text-color);
    position: relative;
    padding-left: 20px;
}

.cases .tab-pane li::before {
    content: "\2022"; /* 使用圆点 */
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

.cases .testimonial-card {
    background: var(--white); /* 调整背景色 */
    padding: 40px; /* 调整内边距 */
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    margin-top: 60px; /* 调整上边距 */
    text-align: right;
    font-style: italic;
    color: var(--dark-gray);
}

.cases .testimonial-quote {
    font-size: 1.4em; /* 调整字体大小 */
    margin-bottom: 25px;
}

.cases .testimonial-quote i {
    color: var(--primary-color);
    margin-right: 10px;
}

.cases .testimonial-author {
    font-size: 1.1em;
    font-weight: 600;
    color: var(--text-color);
}

/* 联系我们样式 */
.contact .contact-wrapper {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 50px; /* 调整间距 */
}

.contact .contact-info {
    display: grid;
    gap: 30px;
    background: var(--white); /* 添加卡片背景 */
    padding: 30px; /* 添加内边距 */
    border-radius: 10px; /* 添加圆角 */
    box-shadow: 0 5px 20px rgba(0,0,0,0.08); /* 添加阴影 */
}

.contact .info-item {
    display: flex; /* info-item 内部使用flex排列图标和详情 */
    align-items: center;
    gap: 20px;
    /* 移除 info-box 原有的背景、内边距、阴影等样式 */
}

.contact .info-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8em; /* 调整图标大小 */
}

.contact .info-details h3 {
    font-size: 1.3em;
    color: var(--dark-gray);
    margin-bottom: 5px;
}

.contact .info-details p {
    font-size: 1.1em;
    color: var(--text-color);
}

.contact .contact-form {
    background: var(--white); /* 表单背景色 */
    padding: 30px; /* 调整内边距 */
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
}

.contact .form-title {
    font-size: 1.8em;
    color: var(--dark-gray);
    margin-bottom: 30px;
    text-align: center;
}

.contact .form-group {
    margin-bottom: 20px; /* 调整底部间距 */
    display: flex; /* 使用Flexbox布局 */
    align-items: center; /* 垂直居中对齐 */
    gap: 15px; /* 标签和输入框之间的间距 */
}

.contact .form-group label {
    display: block; /* 保持label为块级元素 */
    margin-bottom: 0; /* 移除底部margin */
    font-weight: 600;
    color: var(--dark-gray);
    font-size: 1.1em;
    flex-shrink: 0; /* 防止标签收缩 */
    width: 100px; /* 给标签固定宽度 */
}

.contact .form-group input,
.contact .form-group textarea {
    width: 100%; /* 输入框占据剩余宽度 */
    padding: 10px; /* 调整内边距 */
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 1em;
    transition: border-color 0.3s ease-in-out;
    flex-grow: 1; /* 允许输入框增长 */
}

/* 调整textarea的高度 */
.contact .form-group textarea {
    height: 100px; /* 固定textarea高度 */
    resize: vertical; /* 允许垂直调整大小 */
}

/* 调整移动端form-group布局 */
@media (max-width: 576px) {
    .contact .form-group {
        flex-direction: column; /* 在小屏幕上变回垂直布局 */
        align-items: flex-start;
        gap: 8px; /* 调整垂直间距 */
    }

    .contact .form-group label {
        width: auto; /* 移除固定宽度 */
    }
}

.contact .btn-primary {
    width: 100%; /* 按钮宽度 */
    padding: 15px; /* 调整内边距 */
    font-size: 1.2em;
}

/* 底部样式 */
.footer {
    background: var(--dark-gray); /* 调整背景色 */
    color: var(--white);
    padding: 60px 0 30px; /* 调整内边距 */
}

.footer .container {
    display: flex;
    flex-direction: column; /* 垂直布局 */
    align-items: center;
}

.footer-logo {
    margin-bottom: 40px;
}

.footer-logo img {
    height: 50px; /* 调整logo大小 */
    filter: brightness(0) invert(1); /* 使logo变为白色 */
}

.footer-links {
    display: flex; /* 使用flex布局 */
    gap: 50px; /* 调整间距 */
    margin-bottom: 40px;
    flex-wrap: wrap; /* 允许换行 */
    justify-content: center;
}

.footer-col {
    text-align: center;
}

.footer-col h3 {
    margin-bottom: 25px;
    font-size: 1.3em;
    color: var(--accent-color); /* 标题颜色 */
}

.footer-col ul {
    list-style: none;
    padding: 0;
}

.footer-col ul li {
    margin-bottom: 12px;
}

.footer-col a {
    color: var(--medium-gray); /* 链接颜色 */
    text-decoration: none;
    font-size: 1.1em;
    transition: color 0.3s ease-in-out;
}

.footer-col a:hover {
    color: var(--white);
}

.footer-bottom {
    text-align: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255,255,255,0.15);
    width: 100%;
    font-size: 1em;
    color: var(--medium-gray);
}

/* 响应式设计 */
@media (max-width: 992px) {
    .section-header h2 {
        font-size: 2.2em;
    }

    .section-desc {
        font-size: 1.1em;
    }

    .main-title {
        font-size: 3.2em;
    }

    .sub-title {
        font-size: 1.8em;
    }

    .hero-text {
        font-size: 1.2em;
    }

    .new-policy .policy-grid,
    .new-policy .impact-cards-container,
    .new-policy .challenge-solution-grid,
    .services .service-highlights,
    .services .process-steps,
    .services .process-info,
    .why-choose-us-main .why-choose-list,
    .contact .contact-wrapper {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 25px;
    }

    .cases .cases-wrapper {
        flex-direction: column;
        gap: 30px;
    }

    .cases .case-content {
        min-width: unset;
        width: 100%;
    }

    .footer-links {
        gap: 30px;
    }
}

@media (max-width: 768px) {
    .navbar {
        padding: 10px 0;
    }

    .logo-img {
        height: 35px;
    }

    .nav-menu {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--white);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        padding: 20px 0;
        border-top: 1px solid var(--border-color);
    }

    .nav-menu.active {
        display: flex;
    }

    .nav-menu li {
        text-align: center;
        margin: 10px 0;
    }

    .nav-link::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .menu-toggle {
        display: flex;
    }

    .hero {
        padding: 120px 0 60px 0; /* 调整小屏幕上下内边距 */
    }

    .main-title {
        font-size: 2.8em;
    }

    .sub-title {
        font-size: 1.5em;
    }

    .hero-text {
        font-size: 1em;
    }

    .section {
        padding: 60px 0;
    }

    .section-header {
        margin-bottom: 40px;
    }

    .section-header h2 {
        font-size: 2em;
    }

    .section-desc {
        font-size: 1em;
    }

    .new-policy .policy-grid,
    .new-policy .impact-cards-container,
    .new-policy .challenge-solution-grid,
    .services .service-highlights,
    .services .process-steps,
    .services .process-info,
    .why-choose-us-main .why-choose-list,
    .contact .contact-wrapper {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .new-policy .policy-feature-card,
    .new-policy .impact-card,
    .new-policy .challenge-card,
    .new-policy .solution-card,
    .services .highlight-card,
    .services .process-info,
    .contact .info-box,
    .contact .contact-form,
    .cases .case-content,
    .cases .testimonial-card {
        padding: 25px;
    }

    .cases .case-images {
        min-height: 300px; /* 调整图片容器高度 */
    }

    .cases .tab-btn {
        font-size: 1em;
        padding: 10px 15px;
    }

    .cases .testimonial-card {
        padding: 30px;
    }

    .footer {
        padding: 40px 0 20px;
    }

    .footer-logo {
        margin-bottom: 30px;
    }

    .footer-links {
        flex-direction: column;
        gap: 20px;
    }

    .footer-col h3 {
        margin-bottom: 15px;
    }

    .footer-col ul li {
        margin-bottom: 8px;
    }
}

@media (max-width: 576px) {
    .main-title {
        font-size: 2.2em;
    }

    .sub-title {
        font-size: 1.3em;
    }

    .hero-text {
        font-size: 0.9em;
        margin-bottom: 40px; /* 调整底部间距 */
    }

    .section-header h2 {
        font-size: 1.8em;
    }

    .section-desc {
        font-size: 0.9em;
    }

    .btn {
        padding: 10px 25px;
        font-size: 1em;
    }

    .new-policy .policy-feature-card h4,
    .new-policy .impact-card h5,
    .new-policy .price-impact-title,
    .new-policy .our-value-section h3,
    .services .card-content h3,
    .services .process-title,
    .services .info-item h4,
    .cases .tab-pane h3,
    .contact .form-title,
    .footer-col h3 {
        font-size: 1.5em;
    }

    .cases .testimonial-quote {
        font-size: 1.2em;
    }
}

/* 动画效果 */
@-webkit-keyframes fadeIn {
    from { opacity: 0; -webkit-transform: translateY(20px); } /* Added for compatibility */
    to { opacity: 1; -webkit-transform: translateY(0); } /* Added for compatibility */
}

@-moz-keyframes fadeIn {
    from { opacity: 0; -moz-transform: translateY(20px); } /* Added for compatibility */
    to { opacity: 1; -moz-transform: translateY(0); } /* Added for compatibility */
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 医保合规热点讨论板块样式 */
.compliance-hot-topics {
    background-color: #f9f9f9; /* 与其他部分背景色略有区别 */
    padding: 60px 0;
}

.compliance-hot-topics .section-header h2 {
    color: var(--primary-color);
}

.compliance-hot-topics .section-header .section-desc {
    color: var(--text-color-light);
    margin-bottom: 40px;
}

.accordion {
    max-width: 800px;
    margin: 0 auto;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden; /* 确保圆角生效 */
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
}

.accordion-item {
    border-bottom: 1px solid var(--border-color);
}

.accordion-item:last-child {
    border-bottom: none;
}

.accordion-header {
    background-color: #fff;
    color: var(--text-color);
    cursor: pointer;
    padding: 18px 25px;
    width: 100%;
    text-align: left;
    border: none;
    outline: none;
    font-size: 1.1rem;
    font-weight: 600;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.accordion-header .icon {
    margin-right: 15px;
    color: var(--secondary-color);
    font-size: 1.3rem;
}

.accordion-header .arrow {
    font-size: 0.9rem;
    transition: transform 0.3s ease;
    color: var(--text-color-light);
}

.accordion-header.active .arrow {
    transform: rotate(180deg);
}

.accordion-header:hover {
    background-color: #f3f3f3;
}

.accordion-content {
    background-color: #ffffff;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out, padding 0.3s ease-out; /* 添加padding过渡 */
    padding: 0 25px; /* 关闭状态时无上下padding */
}

.accordion-content.active {
    max-height: 500px; /* Adjust as needed, ensure it's enough for content */
    /* opacity: 1; */ /* Already handled by transition */
}

.accordion-content .content-inner {
    padding: 20px 25px;
    padding-bottom: 20px; /* 确保底部有足够空间 */
    background-color: #fff; /* Ensure content background is white */
    border-top: 1px dashed var(--border-color-light);
}

.accordion-content .content-inner p {
    color: var(--text-color-dark);
    font-size: 0.95rem;
    line-height: 1.7;
    margin-bottom: 10px;
}

.accordion-content .content-inner ul {
    list-style: none;
    padding-left: 5px;
}

.accordion-content .content-inner ul li {
    color: var(--text-color);
    font-size: 0.9rem;
    line-height: 1.8;
    margin-bottom: 8px;
    padding-left: 25px;
    position: relative;
}

.accordion-content .content-inner ul li::before {
    content: '\f058'; /* FontAwesome check-circle solid */
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    color: var(--primary-color);
    position: absolute;
    left: 0;
    top: 1px;
}

/* 响应式调整 (可选，如果需要更细致的调整) */
@media (max-width: 768px) {
    .accordion-header {
        padding: 15px 20px;
        font-size: 1rem;
    }
    .accordion-header .icon {
        font-size: 1.1rem;
        margin-right: 10px;
    }
    .accordion-content.active {
        padding: 15px 20px;
    }
    .accordion-content .content-inner ul li {
        font-size: 0.85rem;
    }
}

/* 服务详情部分样式 */
.service-details {
    background-color: var(--white);
}

.service-details h3 {
    font-size: 1.8em;
    color: var(--primary-color);
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.service-details h3 i {
    color: var(--accent-color);
}

.service-details h4 {
    font-size: 1.4em;
    color: var(--dark-gray);
    margin-bottom: 15px;
}

/* 服务目的样式 */
.service-purpose {
    margin-bottom: 60px;
}

.purpose-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.purpose-item {
    background: var(--light-gray);
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.05);
    transition: transform 0.3s ease;
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.purpose-item:hover {
    transform: translateY(-5px);
}

.purpose-icon {
    font-size: 1.5em;
    color: var(--primary-color);
    flex-shrink: 0;
}

/* 适用机构样式 */
.target-hospitals {
    margin-bottom: 60px;
}

.hospital-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.hospital-type-card {
    background: var(--white);
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid var(--border-color);
}

.hospital-type-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.hospital-icon {
    font-size: 2.5em;
    color: var(--accent-color);
    margin-bottom: 20px;
}

/* 价值亮点样式 */
.value-highlights {
    margin-bottom: 60px;
}

.value-table-container {
    overflow-x: auto;
    margin-top: 30px;
}

.value-table {
    width: 100%;
    border-collapse: collapse;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.value-table th,
.value-table td {
    padding: 15px 20px;
    text-align: left;
    border: 1px solid var(--border-color);
}

.value-table th {
    background-color: var(--primary-color);
    color: var(--white);
    font-weight: 600;
}

.value-table tr:nth-child(even) {
    background-color: var(--light-gray);
}

.value-table tr:hover {
    background-color: rgba(0,86,179,0.05);
}

/* 服务内容样式 */
.service-content {
    margin-bottom: 60px;
}

.team-intro {
    font-size: 1.2em;
    margin-bottom: 30px;
    color: var(--dark-gray);
    font-style: italic;
}

.service-table-container {
    overflow-x: auto;
    margin-top: 30px;
}

.service-table {
    width: 100%;
    border-collapse: collapse;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.service-table th,
.service-table td {
    padding: 15px 20px;
    text-align: left;
    border: 1px solid var(--border-color);
}

.service-table th {
    background-color: var(--primary-color);
    color: var(--white);
    font-weight: 600;
}

.service-table tr:nth-child(even) {
    background-color: var(--light-gray);
}

.service-table tr:hover {
    background-color: rgba(0,86,179,0.05);
}

/* 交付标准与报价样式 */
.pricing-options {
    margin-bottom: 60px;
}

.pricing-table-container {
    overflow-x: auto;
    margin-top: 30px;
}

.pricing-table {
    width: 100%;
    border-collapse: collapse;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.pricing-table th,
.pricing-table td {
    padding: 15px 20px;
    text-align: left;
    border: 1px solid var(--border-color);
}

.pricing-table th {
    background-color: var(--primary-color);
    color: var(--white);
    font-weight: 600;
}

.pricing-table tr:nth-child(even) {
    background-color: var(--light-gray);
}

.pricing-table tr:hover {
    background-color: rgba(0,86,179,0.05);
}

.pricing-note {
    margin-top: 20px;
    font-style: italic;
    color: var(--dark-gray);
}

/* 精要总结样式 */
.success-summary {
    margin-bottom: 40px;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-top: 30px;
    margin-bottom: 40px;
}

.summary-card {
    background: var(--white);
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    text-align: center;
    transition: transform 0.3s ease;
    border: 1px solid var(--border-color);
}

.summary-card:hover {
    transform: translateY(-5px);
}

.summary-icon {
    font-size: 2.5em;
    color: var(--accent-color);
    margin-bottom: 20px;
}

.final-cta {
    text-align: center;
    margin-top: 50px;
    padding: 40px;
    background: var(--light-gray);
    border-radius: 10px;
}

.final-cta p {
    font-size: 1.3em;
    margin-bottom: 30px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* ========== 现代化新增样式 ========== */

/* 通用样式 */
.section {
    padding: var(--spacing-2xl) 0;
    background-color: var(--bg-primary);
}

.section:nth-child(even) {
    background-color: var(--bg-secondary);
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    line-height: 1.2;
}

.section-desc {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* 问题识别部分样式 */
.problem-section {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

.policy-impact {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
    margin-bottom: var(--spacing-2xl);
}

.impact-visual {
    background: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

.impact-chart {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.chart-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.chart-bar {
    height: 40px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 var(--spacing-md);
    position: relative;
    overflow: hidden;
}

.chart-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    width: var(--percentage, 0%);
    transition: width 2s ease-out;
}

.percentage {
    color: white;
    font-weight: 700;
    font-size: 1.1rem;
    z-index: 1;
}

.chart-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.impact-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.change-points {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.change-item {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-start;
}

.change-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.change-text h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.change-text p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* 挑战部分样式 */
.challenges-section {
    margin-bottom: var(--spacing-2xl);
}

.challenges-section h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.challenges-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.challenge-item {
    background: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--danger-color);
    transition: all 0.3s ease;
}

.challenge-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.challenge-number {
    width: 40px;
    height: 40px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
    margin-bottom: var(--spacing-md);
}

.challenge-content h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.challenge-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.challenge-impact {
    display: flex;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
}

.impact-tag {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
}

/* 紧迫性提醒 */
.urgency-alert {
    background: linear-gradient(135deg, var(--warning-color), #f97316);
    color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
}

.alert-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    flex-shrink: 0;
}

.alert-content {
    flex: 1;
}

.alert-content h4 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
}

.alert-content p {
    font-size: 1.1rem;
    opacity: 0.9;
    line-height: 1.6;
}

.alert-action {
    flex-shrink: 0;
}

/* 解决方案部分样式 */
.solution-section {
    background: var(--bg-primary);
}

.solution-overview {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
    margin-bottom: var(--spacing-2xl);
}

.overview-visual {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

.solution-flow {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.flow-step {
    text-align: center;
    flex: 1;
}

.step-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin: 0 auto var(--spacing-md);
    box-shadow: var(--shadow-md);
}

.flow-step h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.flow-step p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.flow-arrow {
    color: var(--primary-color);
    font-size: 1.5rem;
    flex-shrink: 0;
}

.overview-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.advantages-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.advantage-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
}

.advantage-item:hover {
    transform: translateX(10px);
    box-shadow: var(--shadow-md);
}

.advantage-item i {
    color: var(--success-color);
    font-size: 1.2rem;
}

.advantage-item span {
    font-weight: 500;
    color: var(--text-primary);
}

/* 核心能力样式 */
.core-capabilities {
    margin-bottom: var(--spacing-2xl);
}

.core-capabilities h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.capabilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.capability-card {
    background: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
}

.capability-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.capability-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin: 0 auto var(--spacing-lg);
}

.capability-card h4 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.capability-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.capability-features {
    display: flex;
    gap: var(--spacing-xs);
    justify-content: center;
    flex-wrap: wrap;
}

.feature-tag {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
}

/* 价值展示部分样式 */
.value-section {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

.value-matrix {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.matrix-item {
    background: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.matrix-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.matrix-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.matrix-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin: 0 auto var(--spacing-lg);
}

.matrix-icon.compliance {
    background: linear-gradient(135deg, var(--success-color), #059669);
}

.matrix-icon.efficiency {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.matrix-icon.quality {
    background: linear-gradient(135deg, var(--warning-color), #f97316);
}

.matrix-icon.innovation {
    background: linear-gradient(135deg, var(--accent-color), #0891b2);
}

.matrix-item h4 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.matrix-content {
    margin-bottom: var(--spacing-md);
}

.value-point {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.point-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.point-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.matrix-item p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* 临床价值流程样式 */
.clinical-value {
    margin-bottom: var(--spacing-2xl);
}

.clinical-value h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.value-flow {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.flow-item {
    background: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.flow-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.flow-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.flow-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
    margin: 0 auto var(--spacing-md);
}

.flow-item h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.flow-metrics {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
}

.metric {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
}

.flow-item p {
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 0.95rem;
}

/* ROI计算器样式 */
.roi-calculator {
    background: var(--bg-primary);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
}

.roi-calculator h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.roi-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

.roi-visual {
    text-align: center;
}

.roi-chart {
    position: relative;
    display: inline-block;
}

.chart-circle {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: conic-gradient(var(--primary-color) 0deg 270deg, var(--border-light) 270deg 360deg);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.chart-circle::before {
    content: '';
    position: absolute;
    width: 140px;
    height: 140px;
    background: var(--bg-primary);
    border-radius: 50%;
}

.roi-percentage {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    z-index: 1;
}

.roi-label {
    font-size: 1rem;
    color: var(--text-secondary);
    z-index: 1;
}

.roi-breakdown {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.roi-item h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
}

.roi-item ul {
    list-style: none;
    padding: 0;
}

.roi-item li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-light);
    color: var(--text-secondary);
}

.roi-item li:last-child {
    border-bottom: none;
}

.roi-item strong {
    color: var(--primary-color);
    font-weight: 600;
}

/* 成功案例部分样式 */
.cases-section {
    background: var(--bg-primary);
}

.case-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.stat-card {
    background: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.stat-card .stat-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
    margin: 0 auto var(--spacing-md);
}

.stat-card .stat-content {
    text-align: center;
}

.stat-card .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    display: block;
    margin-bottom: var(--spacing-xs);
}

.stat-card .stat-label {
    color: var(--text-secondary);
    font-weight: 500;
}

/* 典型案例样式 */
.featured-cases {
    margin-bottom: var(--spacing-2xl);
}

.featured-cases h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.cases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.case-card {
    background: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
}

.case-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.case-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.case-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.case-header h4 {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.case-type {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
}

.case-results {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.result-item {
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    text-align: center;
}

.result-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    display: block;
    margin-bottom: var(--spacing-xs);
}

.result-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.case-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* 服务详情部分样式 */
.service-details-section {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

/* 服务流程时间线 */
.service-process {
    margin-bottom: var(--spacing-2xl);
}

.service-process h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.process-timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.process-timeline::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(180deg, var(--primary-color), var(--accent-color));
}

.timeline-item {
    position: relative;
    margin-bottom: var(--spacing-2xl);
    padding-left: 80px;
}

.timeline-marker {
    position: absolute;
    left: 0;
    top: 0;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
    z-index: 1;
}

.step-number {
    color: white;
    font-weight: 700;
    font-size: 1.2rem;
}

.timeline-content {
    background: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border-left: 4px solid var(--primary-color);
}

.timeline-content h4 {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.timeline-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.deliverable {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    font-weight: 500;
    display: inline-block;
}

/* 服务团队样式 */
.service-team {
    margin-bottom: var(--spacing-2xl);
}

.service-team h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.team-member {
    background: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.member-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin: 0 auto var(--spacing-lg);
}

.team-member h4 {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.team-member p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.team-member ul {
    list-style: none;
    padding: 0;
    text-align: left;
}

.team-member li {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.team-member li::before {
    content: '✓';
    color: var(--success-color);
    font-weight: 700;
}

/* 服务保障样式 */
.service-guarantee {
    margin-bottom: var(--spacing-2xl);
}

.service-guarantee h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.guarantee-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.guarantee-item {
    background: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
}

.guarantee-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.guarantee-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--success-color), #059669);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
    margin: 0 auto var(--spacing-md);
}

.guarantee-item h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.guarantee-item p {
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 0.95rem;
}

/* 价格方案样式 */
.pricing-section {
    background: var(--bg-primary);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
}

.pricing-section h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.pricing-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
}

.pricing-card {
    background: var(--bg-primary);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.pricing-card.basic {
    border-color: var(--border-medium);
}

.pricing-card.premium {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-lg);
}

.pricing-card.enterprise {
    border-color: var(--accent-color);
}

.card-badge {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 600;
}

.card-header h4 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.price {
    margin-bottom: var(--spacing-lg);
}

.currency {
    font-size: 1.2rem;
    color: var(--text-secondary);
    vertical-align: top;
}

.amount {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.period {
    font-size: 1rem;
    color: var(--text-secondary);
}

.card-features ul {
    list-style: none;
    padding: 0;
    text-align: left;
    margin-bottom: var(--spacing-lg);
}

.card-features li {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) 0;
    color: var(--text-secondary);
    border-bottom: 1px solid var(--border-light);
}

.card-features li:last-child {
    border-bottom: none;
}

.card-features i {
    color: var(--success-color);
    font-size: 1rem;
}

.card-footer {
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-light);
}

.suitable {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: 0.9rem;
    font-weight: 500;
}

.pricing-note {
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.6;
}

.pricing-note i {
    color: var(--info-color);
    margin-right: var(--spacing-xs);
}

/* 联系我们部分样式 */
.contact-section {
    background: var(--bg-primary);
}

.contact-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.contact-card {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.contact-card .card-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
    margin: 0 auto var(--spacing-md);
}

.contact-card h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.contact-card p {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.contact-note {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* 咨询表单样式 */
.consultation-form {
    background: var(--bg-secondary);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
    margin-bottom: var(--spacing-2xl);
}

.form-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.form-header h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.form-header p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.modern-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: var(--spacing-md);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-md);
    font-size: 1rem;
    transition: all 0.2s ease;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-actions {
    text-align: center;
    margin-top: var(--spacing-xl);
}

.privacy-note {
    margin-top: var(--spacing-md);
    font-size: 0.9rem;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
}

.privacy-note i {
    color: var(--success-color);
}

/* 快速响应承诺 */
.response-promise {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.promise-item {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
}

.promise-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.promise-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--success-color), #059669);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin: 0 auto var(--spacing-md);
}

.promise-item h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.promise-item p {
    color: var(--text-secondary);
    font-size: 0.95rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-visual {
        order: -1;
        margin-bottom: var(--spacing-xl);
    }

    .floating-cards {
        width: 300px;
        height: 300px;
    }

    .policy-impact,
    .solution-overview,
    .roi-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .menu-toggle {
        display: flex;
    }

    .hero {
        padding: 100px 0 60px;
    }

    .main-title {
        font-size: 2.5rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .challenges-grid,
    .capabilities-grid,
    .value-matrix,
    .cases-grid,
    .team-grid,
    .guarantee-grid,
    .pricing-cards {
        grid-template-columns: 1fr;
    }

    .hero-stats {
        justify-content: center;
    }

    .hero-actions {
        justify-content: center;
    }

    .contact-cards,
    .response-promise {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .main-title {
        font-size: 2rem;
    }

    .sub-title {
        font-size: 1rem;
    }

    .section-header h2 {
        font-size: 1.8rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .floating-cards {
        width: 250px;
        height: 250px;
    }

    .timeline-item {
        padding-left: 60px;
    }

    .timeline-marker {
        width: 40px;
        height: 40px;
    }

    .step-number {
        font-size: 1rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 滚动动画 */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* 加载动画 */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}
