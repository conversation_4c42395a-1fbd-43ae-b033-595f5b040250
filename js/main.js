/**
 * 一脉阳光医院放射检查项目合规化和标准化改造服务 - 现代化网站脚本
 * 版本: 2.0
 * 更新日期: 2024-07-30
 */

class ModernWebsite {
    constructor() {
        this.init();
    }

    init() {
        this.setupNavigation();
        this.setupScrollAnimations();
        this.setupFormHandling();
        this.setupInteractiveElements();
        this.setupPerformanceOptimizations();
    }

    // 导航栏功能
    setupNavigation() {
        const navbar = document.getElementById('navbar');
        const mobileMenu = document.getElementById('mobile-menu');
        const navMenu = document.querySelector('.nav-menu');

        // 滚动效果
        let ticking = false;
        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    if (window.scrollY > 50) {
                        navbar.classList.add('scrolled');
                    } else {
                        navbar.classList.remove('scrolled');
                    }
                    this.highlightCurrentSection();
                    ticking = false;
                });
                ticking = true;
            }
        });

        // 移动端菜单
        if (mobileMenu && navMenu) {
            mobileMenu.addEventListener('click', () => {
                mobileMenu.classList.toggle('active');
                navMenu.classList.toggle('active');
                document.body.classList.toggle('menu-open');
            });
        }

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    const headerOffset = 80;
                    const elementPosition = target.getBoundingClientRect().top;
                    const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                    window.scrollTo({
                        top: offsetPosition,
                        behavior: 'smooth'
                    });

                    // 关闭移动端菜单
                    if (mobileMenu && navMenu) {
                        mobileMenu.classList.remove('active');
                        navMenu.classList.remove('active');
                        document.body.classList.remove('menu-open');
                    }
                }
            });
        });
    }

    // 滚动动画
    setupScrollAnimations() {
        // 通用滚动动画观察器
        const animateObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        // 为所有需要动画的元素添加观察
        document.querySelectorAll('.animate-on-scroll').forEach(element => {
            animateObserver.observe(element);
        });

        // 数字动画
        this.setupCounterAnimations();

        // 图表动画
        this.setupChartAnimations();
    }

    // 数字计数动画
    setupCounterAnimations() {
        const counters = document.querySelectorAll('.stat-number, .percentage, .roi-percentage');
        const counterObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateCounter(entry.target);
                    counterObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        counters.forEach(counter => {
            counterObserver.observe(counter);
        });
    }

    // 计数器动画函数
    animateCounter(element) {
        const target = parseInt(element.textContent.replace(/[^\d]/g, ''));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }

            const suffix = element.textContent.replace(/[\d]/g, '');
            element.textContent = Math.floor(current) + suffix;
        }, 16);
    }

    // 图表动画
    setupChartAnimations() {
        const chartBars = document.querySelectorAll('.chart-bar');
        const chartObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const percentage = entry.target.dataset.percentage;
                    entry.target.style.setProperty('--percentage', percentage + '%');
                    chartObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        chartBars.forEach(bar => {
            chartObserver.observe(bar);
        });
    }

    // 表单处理
    setupFormHandling() {
        const consultForm = document.getElementById('consultForm');
        if (consultForm) {
            consultForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFormSubmission(consultForm);
            });

            // 实时验证
            const inputs = consultForm.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateField(input);
                });
            });
        }
    }

    // 表单提交处理
    handleFormSubmission(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);

        if (this.validateForm(data)) {
            // 显示加载状态
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';
            submitBtn.disabled = true;

            // 模拟提交过程
            setTimeout(() => {
                this.showSuccessMessage();
                form.reset();
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 2000);
        }
    }

    // 表单验证
    validateForm(data) {
        let isValid = true;
        const errors = [];

        // 验证医院名称
        if (!data.hospitalName?.trim()) {
            errors.push('请输入医院名称');
            isValid = false;
        }

        // 验证医院类型
        if (!data.hospitalType) {
            errors.push('请选择医院类型');
            isValid = false;
        }

        // 验证联系人
        if (!data.contactPerson?.trim()) {
            errors.push('请输入联系人姓名');
            isValid = false;
        }

        // 验证手机号
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(data.phone)) {
            errors.push('请输入有效的手机号码');
            isValid = false;
        }

        // 验证邮箱（可选）
        if (data.email?.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
            errors.push('请输入有效的电子邮箱');
            isValid = false;
        }

        if (!isValid) {
            this.showErrorMessage(errors);
        }

        return isValid;
    }

    // 字段验证
    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let message = '';

        switch (field.name) {
            case 'hospitalName':
                if (!value) {
                    isValid = false;
                    message = '请输入医院名称';
                }
                break;
            case 'contactPerson':
                if (!value) {
                    isValid = false;
                    message = '请输入联系人姓名';
                }
                break;
            case 'phone':
                if (!/^1[3-9]\d{9}$/.test(value)) {
                    isValid = false;
                    message = '请输入有效的手机号码';
                }
                break;
            case 'email':
                if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                    isValid = false;
                    message = '请输入有效的电子邮箱';
                }
                break;
        }

        this.updateFieldValidation(field, isValid, message);
        return isValid;
    }

    // 更新字段验证状态
    updateFieldValidation(field, isValid, message) {
        const formGroup = field.closest('.form-group');
        const errorElement = formGroup.querySelector('.field-error') ||
                           this.createErrorElement();

        if (!isValid) {
            field.classList.add('error');
            errorElement.textContent = message;
            if (!formGroup.querySelector('.field-error')) {
                formGroup.appendChild(errorElement);
            }
        } else {
            field.classList.remove('error');
            const existingError = formGroup.querySelector('.field-error');
            if (existingError) {
                existingError.remove();
            }
        }
    }

    // 创建错误提示元素
    createErrorElement() {
        const errorElement = document.createElement('span');
        errorElement.className = 'field-error';
        errorElement.style.color = 'var(--danger-color)';
        errorElement.style.fontSize = '0.8rem';
        errorElement.style.marginTop = 'var(--spacing-xs)';
        return errorElement;
    }

    // 交互元素设置
    setupInteractiveElements() {
        // 卡片悬停效果
        this.setupCardHoverEffects();

        // 按钮点击效果
        this.setupButtonEffects();

        // 工具提示
        this.setupTooltips();
    }

    // 卡片悬停效果
    setupCardHoverEffects() {
        const cards = document.querySelectorAll('.challenge-item, .capability-card, .matrix-item, .case-card');

        cards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = '';
            });
        });
    }

    // 按钮效果
    setupButtonEffects() {
        const buttons = document.querySelectorAll('.btn');

        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                // 创建波纹效果
                const ripple = document.createElement('span');
                const rect = button.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s linear;
                    pointer-events: none;
                `;

                button.style.position = 'relative';
                button.style.overflow = 'hidden';
                button.appendChild(ripple);

                setTimeout(() => ripple.remove(), 600);
            });
        });
    }

    // 工具提示
    setupTooltips() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');

        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target, e.target.dataset.tooltip);
            });

            element.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    }

    // 显示工具提示
    showTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = text;
        tooltip.style.cssText = `
            position: absolute;
            background: var(--bg-dark);
            color: white;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: 0.8rem;
            z-index: 1000;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;

        document.body.appendChild(tooltip);

        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + rect.width / 2 - tooltip.offsetWidth / 2 + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';

        setTimeout(() => tooltip.style.opacity = '1', 10);
        this.currentTooltip = tooltip;
    }

    // 隐藏工具提示
    hideTooltip() {
        if (this.currentTooltip) {
            this.currentTooltip.style.opacity = '0';
            setTimeout(() => {
                if (this.currentTooltip) {
                    this.currentTooltip.remove();
                    this.currentTooltip = null;
                }
            }, 300);
        }
    }

    // 性能优化
    setupPerformanceOptimizations() {
        // 图片懒加载
        this.setupLazyLoading();

        // 预加载关键资源
        this.preloadCriticalResources();
    }

    // 图片懒加载
    setupLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('loading');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => {
            img.classList.add('loading');
            imageObserver.observe(img);
        });
    }

    // 预加载关键资源
    preloadCriticalResources() {
        const criticalImages = [
            'images/hero-bg.jpg',
            'images/rimag_logo.png'
        ];

        criticalImages.forEach(src => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = src;
            document.head.appendChild(link);
        });
    }

    // 高亮当前导航项
    highlightCurrentSection() {
        const sections = document.querySelectorAll('section');
        const navLinks = document.querySelectorAll('.nav-link');

        let currentSectionId = '';
        const scrollPosition = window.scrollY + 150;

        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;

            if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                currentSectionId = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${currentSectionId}`) {
                link.classList.add('active');
            }
        });
    }

    // 显示成功消息
    showSuccessMessage() {
        this.showNotification('感谢您的咨询！我们会在1小时内与您联系。', 'success');
    }

    // 显示错误消息
    showErrorMessage(errors) {
        this.showNotification('请检查表单信息：\n' + errors.join('\n'), 'error');
    }

    // 显示通知
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-primary);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            max-width: 400px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        if (type === 'success') {
            notification.style.borderLeftColor = 'var(--success-color)';
        } else if (type === 'error') {
            notification.style.borderLeftColor = 'var(--danger-color)';
        }

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 10);

        // 自动关闭
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => notification.remove(), 300);
        }, 5000);

        // 手动关闭
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => notification.remove(), 300);
        });
    }
}

// 初始化网站
document.addEventListener('DOMContentLoaded', () => {
    new ModernWebsite();

    // 添加CSS动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        .field-error {
            display: block;
            color: var(--danger-color);
            font-size: 0.8rem;
            margin-top: var(--spacing-xs);
        }

        .form-group input.error,
        .form-group select.error,
        .form-group textarea.error {
            border-color: var(--danger-color);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .notification {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: var(--spacing-md);
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .notification-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: var(--spacing-xs);
            border-radius: var(--radius-sm);
            transition: background-color 0.2s ease;
        }

        .notification-close:hover {
            background-color: var(--bg-secondary);
        }
    `;
    document.head.appendChild(style);
});